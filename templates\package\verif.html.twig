{% extends 'base.html.twig' %}

{% block title %}Package {{ package.id }}{% endblock %}

{% block body %}
<style>
td{
    text-align: center;
    vertical-align: middle;
    font-size: 12px;
}

th{
    text-align: center;
    vertical-align: middle;
    background-color: #004080!important;
    color: white!important;
    border: none!important;
    font-size: 10px!important;
}

.badge-hover:hover{
    background-color: #004080!important;
    transition: background-color 0.3s ease;
}

.badge-hover{
    transition: background-color 0.3s ease;
    cursor: pointer;
}

.fw-500{
    font-weight: 500;
}

label{
    white-space: nowrap;
    user-select: none;
}

#document-form label{
    min-width: 13rem!important;
    width: 13rem!important;
    max-width: 13rem!important;
}

.form-label-sm{
    font-size: .875rem;
}

.first{
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
    width: 90%;
}

.second{
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    width: 15%;
}

.selected-row td{
    background-color:rgba(62, 156, 250, 0.45)!important;
    border: 1px rgba(62, 156, 250, 0.45)!important;
}
 
textarea{
    transition: all 0.3s ease;
}

</style>

<div class="m-0 rounded-0">
    <div class="card border-0">
        <div style="background-color: #004080;" class="card-header border-0 text-white d-flex justify-content-between align-items-center rounded-0">
            <h3 class="card-title p-0 mb-0">Vérification Package - {{ package.id }}</h3>
        </div>
        <div class="card-body row mx-0 p-0">
            {# DOCUMENT FORM COL #}
            <input type="hidden" id="document_id" name="document_id" value="">
            <div class="border-end col-md-7" id="document-form">
                <div class="row mx-0">
                    <div class="col-md-6">
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="reference">Reference Article (SAP)</label>
                            <div class="d-flex align-items-center w-100">
                                <input type="text" class="form-control form-control-sm first" id="reference" name="reference" placeholder="Reference" required>
                                <input type="text" class="form-control form-control-sm second" id="refRev" name="refRev" placeholder="Rev" required>
                            </div>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="prodDraw">Product Drawing</label>
                            <div class="d-flex align-items-center w-100">
                                <input type="text" class="form-control form-control-sm first" id="prodDraw" name="prodDraw" placeholder="ProdDraw" required>
                                <input type="text" class="form-control form-control-sm second" id="prodDrawRev" name="prodDrawRev" placeholder="Rev" required>
                            </div>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="alias">Alias</label>
                            <div class="w-100 position-relative">
                                <input type="text" class="form-control form-control-sm" id="alias" name="alias" placeholder="Alias" maxlength="40" 
                                    oninput="this.value = this.value.slice(0, 40); document.getElementById('charCount').textContent = this.value.length + '/40';"
                                    onchange="document.getElementById('charCount').textContent = this.value.length + '/40';">
                                <small id="charCount" class="position-absolute end-0 top-0 text-muted mt-2 me-2" style="font-size: 0.7rem;">0/40</small>
                            </div>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="refTitleFra">Title</label>
                            <input type="text" class="form-control form-control-sm" id="refTitleFra" name="refTitleFra" placeholder="Title" required>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="action">Action</label>
                            <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="action" name="action" required title="Action" >
                                <option value="Modification">Modification</option>
                                <option value="Suppression">Suppression</option>
                                <option value="Création">Création</option>
                            </select>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="docType">Type</label>
                            <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="docType" name="docType" required title="Type" >
                                <option value ="PUR" title="PUR">PUR - Purchased </option>
                                <option value ="MOLD" title="MOLD">MOLD - Molded Part (Internal or External)</option>
                                <option value ="MACH" title="MACH">MACH - Machined Part (Internal or External)</option>
                                <option value ="DOC" title="DOC">DOC - PC, ID, E-, AD</option>
                                <option value ="ASSY" title="ASSY">ASSY - Marking, BoM, GA, Assembly, FT for ZPF BoM driven by FT</option>
                            </select>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="rdo">RDO</label>
                            <input type="text" class="form-control form-control-sm" id="rdo" name="rdo" placeholder="RDO" >
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="eccn">ECCN</label>
                            <input type="text" class="form-control form-control-sm" id="eccn" name="eccn" placeholder="ECCN" >
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="materialType">Material Type</label>
                            <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="materialType" name="materialType" required title="Material Type" >
                                <option value ="SEMI-FINISHED PRODUCT" >SEMI-FINISHED PRODUCT</option>
                                <option value ="RAW MATERIAL" >RAW MATERIAL</option>
                                <option value ="PACKAGING" >PACKAGING</option>
                                <option value ="NON VALUATED MATERIAL" >NON VALUATED MATERIAL</option>
                                <option value ="LITERATURE" >LITERATURE</option>
                                <option value ="FINISHED PRODUCT" >FINISHED PRODUCT</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="inventoryImpact">Inventory Impact</label>
                            <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="inventoryImpact" name="inventoryImpact" required title="Inventory Impact" >
                                <option value="TO BE UPDATED" test="Ongoing orders in SCM, orders at suppliers or parts in stock at SCM must be reworkedn ">TO BE UPDATED</option>
                                <option value="TO BE SCRAPPED" test="Every parts in stock, being used in assembly, or being manufactured/ordered must be scrapped ">TO BE SCRAPPED</option>
                                <option value="NO IMPACT" test="No impact on the current stock or any ongoing orders or manufacturing">NO IMPACT</option>
                            </select>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="ex">Ex</label>
                            <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="ex" name="ex" required title="Ex">
                                <option selected value=""></option>
                                <option value="NO">NO</option>
                                <option value="IECES">IECES</option>
                                <option value="CSA">CSA</option>
                                <option value="ATEX">ATEX</option>
                            </select>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="custDrawing">Cust. Drawing</label>
                            <div class="d-flex align-items-center w-100">
                                <input type="text" class="form-control form-control-sm first" id="custDrawing" name="custDrawing" placeholder="Cust. Drawing" >
                                <input type="text" class="form-control form-control-sm second" id="custDrawingRev" name="custDrawingRev" placeholder="Rev" >
                            </div>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="weight">Weight in air</label>
                            <div class="d-flex align-items-center w-100">
                                <input type="number" class="form-control form-control-sm first" id="weight" name="weight" placeholder="Weight" required>
                                <select class="selectpicker" data-style="border btn btn-sm bg-white second" data-width="15%" id="weightUnit" name="weightUnit" required title="Unit" >
                                    <option value=""></option>
                                    <option value="g">g</option>
                                    <option value="kg">kg</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="material">Material</label>
                            <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="material" name="material" title="Material" >
                                <option value=""></option>
                                <option value="FMTP900">FMTP900</option>
                                <option value="FMTP901">FMTP901</option>
                                <option value="FMTP902">FMTP902</option>
                                <option value="FMTP903">FMTP903</option>
                                <option value="FMTP904">FMTP904</option>
                                <option value="FMTP905">FMTP905</option>
                                <option value="FMTP906">FMTP906</option>
                                <option value="FMTP907">FMTP907</option>
                                <option value="FMTP908">FMTP908</option>
                                <option value="FMTP909">FMTP909</option>
                                <option value="FMTP910">FMTP910</option>
                            </select>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="platingSurface">Plating Surface</label>
                            <div class="d-flex align-items-center w-100">
                                <input type="text" class="form-control form-control-sm first" id="platingSurface" name="platingSurface" placeholder="Plating Surface" >
                                <select class="selectpicker" data-style="border btn btn-sm bg-white second" data-width="15%" id="platingSurfaceUnit" name="platingSurfaceUnit" title="Unit" >
                                    <option value=""></option>
                                    <option value="mm²">mm²</option>
                                    <option value="cm²">cm²</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="hts">HTS</label>
                            <input type="text" class="form-control form-control-sm" id="hts" name="hts" placeholder="HTS" >
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="internalMachRec">Recommanded Inhouse Manuf.</label>
                            <div class="w-100">
                                <input type="checkbox" class="form-check-input mt-2 me-2" id="internalMachRec" name="internalMachRec" placeholder="internalMachRec" style="transform: scale(1.5);">
                            </div>
                        </div>
                    </div>
                    <div class="d-flex align-items-center gap-2 w-100">
                        <div class="mt-2 mb-2 w-100">
                            <label class="fw-500 form-label-sm" for="comments">Commentaire</label>
                            <textarea class="form-control form-control-sm" id="comments" name="comments" placeholder="Commentaire" rows="3"></textarea>
                        </div>
                        <button class="btn btn-sm btn-primary" style="display: none" id="update-document">Update</button>
                    </div>
                </div>
            </div>
            <div class="col-md-5 p-0 row-cols-1 mx-0" style="background-color: #F1F3F5;">
                {# PREVIEW row #}
                <iframe id="aletiq_preview" class="col" style="width:100%;height:100%;" src=""></iframe>
            </div>
            <div class="mx-0 p-0 pb-3 row border-top" style="background-color: #EFF1F2;">
                {# COL PACKAGE #}
                <div class="col-11" >
                    <h6 class="fw-500 text-muted m-0">Package info</h6>
                    <div class="d-flex align-items-center justify-content-around">
                        <div class="form-group">
                            <label class="fw-500" for="owner">Propriétaire</label>
                            <p class="fw-bold m-0" ><i class="fas fa-user"></i> {{ package.owner }}</p>
                        </div>
                        <div class="form-group">
                            <label class="fw-500" for="owner">Vérification</label>
                            <p class="fw-bold m-0" ><i class="fas fa-user"></i> {{ package.verif }}</p>
                        </div>
                        <div class="form-group">
                            <label class="fw-500" for="owner">Réservation</label>
                            <p class="fw-bold m-0" >
                                {% if package.ReservationDate is not null %}
                                    {{ package.ReservationDate|date('Y-m-d') }}
                                {% endif %}
                            </p>
                        </div>
                        <div class="form-group">
                            <label class="fw-500" for="owner">Release</label>
                            <p class="fw-bold m-0" >
                                {% if package.DateBE0 is not null %}
                                    {{ package.DateBE0|date('Y-m-d') }}
                                {% endif %}
                            </p>
                        </div>
                        <div class="form-group mt-2">
                            <label class="fw-500" for="activity">Activité</label>
                            <select class="selectpicker" data-style="border btn bg-white btn-sm" data-width="100%" id="activity" name="activity" required title="Activité" >
                                <option value="OSI">OSI</option>
                                <option value="MOB_INDUS">MOB_INDUS</option>
                                <option value="MOB_AERO">MOB_AERO</option>
                                <option value="METHOD">METHOD</option>
                                <option value="ENERGY_SIGNAL">ENERGY_SIGNAL</option>
                                <option value="ENERGY_RENEW">ENERGY_RENEW</option>
                                <option value="ENERGY_POWER">ENERGY_POWER</option>
                            </select>
                        </div>
                        <div class="form-group mt-2">
                            <label class="fw-500" for="ex-package">Ex</label>
                            <select class="selectpicker" data-style="border btn bg-white btn-sm" data-width="100%" id="ex-package" name="ex-package" required title="Ex" >
                                <option value="NO">NO</option>
                                <option value="IECES">IECES</option>
                                <option value="CSA">CSA</option>
                                <option value="ATEX">ATEX</option>
                            </select>
                        </div>
                        <div class="form-group mt-2">
                            <label class="fw-500" for="dmo">DMO</label>
                            <select class="selectpicker" data-style="border btn bg-white btn-sm" data-width="100%" id="dmo" name="dmo[]" required multiple title="DMO" data-live-search="true" data-size="10" data-selected-text-format="count > 2">
                            {% for otp, dmoList in dmobyProjects %}
                                    <optgroup label="{{ otp }}">
                                        {% for dmo in dmoList %}
                                            <option value="{{ dmo.id }}" data-project="{{ otp }}">
                                                {{ dmo.id_dmo }}
                                            </option>
                                        {% endfor %}
                                    </optgroup>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group mt-2">
                            <label class="fw-500" for="project">Projet</label>
                            <select class="selectpicker" data-style="border btn bg-white btn-sm" data-width="100%" id="project" name="project" required data-live-search="true" data-size="10">
                                {% for project in projects %}
                                    {% if package.getProjectRelation is not null and project.otp == package.getProjectRelation.getOTP %}
                                        <option value="{{ project.id }}" selected>{{ project.otp }}</option>
                                    {% else %}
                                        <option value="{{ project.id }}">{{ project.otp }}</option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row mx-0">
                        <label class="fw-500" for="description">Description</label>
                        <textarea class="form-control form-control-sm" id="description" name="description" rows="2" required style="resize: none;">{{ package.description }}</textarea>
                    </div>
                </div>
                <div class="col-1 d-flex flex-column justify-content-center gap-2">
                    <button class="btn btn-sm btn-outline-secondary" id="update">Update</button>
                    <button class="btn btn-sm btn-success" id="verif">Vérification</button>
                    <button class="btn btn-sm btn-danger" id="refuser">Renvoyer</button>
                </div>
            </div>
        </div>
            <div class="col-md-12 p-0" id="div-documents">
                <table class="table table-hover table-sm table-bordered mb-0" id="table-documents">
                    <thead>
                        <tr id="entetes">
                            <th>Action</th>
                            <th colspan="2">Reference</th>
                            <th>ID_ALETIQ</th>
                            <th colspan="2">Product Drawing</th>
                            <th>Title</th>
                            <th>Alias</th>
                            <th>Cust. Drawing</th>
                            <th colspan="2">Doc Type</th>
                            <th>Mat Type</th>
                            <th>Inventory Imp.</th>
                            <th>Ex</th>
                            <th colspan="2">Weight</th>
                            <th colspan="2">Plat. Surface</th>
                            <th>Material</th>
                            <th>ECCN</th>
                            <th>RDO</th>
                            <th>HTS</th>
                            <th>Comments</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for document in package.documents %}
                        <tr class="document_row" id="document_{{ document.id }}">
                            <td class="action" >{{ document.action }}</td>
                            <td class="reference" >{{ document.reference }}</td>
                            <td class="refRev" >{{ document.refRev }}</td>
                            <td class="prodDraw" >
                                <a href="https://app.aletiq.com/parts/preview/id/{{ document.prodDraw }}/revision/{{ document.prodDrawRev }}"
                                    target="_blank"
                                    class="badge bg-primary preview-tooltip">
                                    {{ document.prodDraw }}
                                </a>
                            </td>
                            <td class="prodDrawRev" >{{ document.prodDrawRev }}</td>
                            <td class="refTitleFra" >{{ document.refTitleFra }}</td>
                            <td class="alias" >{{ document.alias }}</td>
                            <td class="custDrawing" >{{ document.custDrawing }}</td>
                            <td class="docType" >{{ document.docType }}</td>
                            <td class="internalMachRec" >{% if document.internalMachRec == 1 %}<img src="{{ asset('scm.png') }}" alt="scm" style="height: 15px;">{% endif %}</td>
                            <td class="materialType" >{{ document.materialType }}</td>
                            <td class="inventoryImpact" >{{ document.inventoryImpact }}</td>
                            <td class="ex">
                                {% if document.ex != 'NO' %}
                                    <span style="color: red;font-weight: 500">{{ document.ex }}</span>
                                {% else %}
                                    {{ document.ex }}
                                {% endif %}
                            </td>
                            <td class="weight" >{{ document.weight }}</td>
                            <td class="weightUnit" >{{ document.weightUnit }}</td>
                            <td class="platingSurface" >{{ document.platingSurface }}</td>
                            <td class="platingSurfaceUnit" >{{ document.platingSurfaceUnit }}</td>
                            <td class="material" >{{ document.material }}</td>
                            <td class="eccn" >{{ document.eccn }}</td>
                            <td class="rdo" >{{ document.rdo }}</td>
                            <td class="hts" >{{ document.hts }}</td>
                            <td class="comments" >
                                {% if document.commentaires|length > 0 %}
                                    <i class="fas fa-comment commentaire-icone" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="{% for comment in document.commentaires %}{% if comment.type == 'principal' %}{{ comment.commentaire }}{% endif %}{% endfor %}"></i>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="modal_release" tabindex="-1" role="dialog" aria-labelledby="modal_release" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal_release">Envoyer en Validation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" >
                <div class="row">
                    <div class="d-flex align-items-center gap-4">
                        <label class="fw-500 form-label-sm" for="valid">Validation</label>
                        <select class="selectpicker" data-style="border btn bg-white btn-sm" data-width="100%" id="valid" name="valid" required data-live-search="true" data-size="10">
                            {% for user in users %}
                                <option value="{{ user.id }}">{{ user }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-success" id="verif_package" >Vérifier</button>
            </div>
        </div>
    </div>
</div>




<script>    
    $('#dmo').selectpicker('val', [{% for dmo in package.dmos %}'{{ dmo.id }}',{% endfor %}]);
    $('#activity').selectpicker('val', '{{ package.activity }}');
    $('#ex-package').selectpicker('val', '{{ package.ex }}');
    $('#project').selectpicker('val', '{{ package.getProjectRelation is not null and package.getProjectRelation.getOTP is not null ? package.getProjectRelation.getOTP : '' }}');

    function handleDMOSelection($select) {
        const selectedOptions = $select.find('option:selected');
        
        if (selectedOptions.length > 0) {
            // Désactive le select project et ajoute l’option du premier DMO sélectionné
            $('#project').empty();
            $('#project').prop('disabled', true);
            $('#project').append('<option value="' + selectedOptions.first().data('project') + '">' + selectedOptions.first().data('project') + '</option>');
            $('#project').selectpicker('destroy');
            $('#project').selectpicker();
            
            // Désactive les options DMO qui ne correspondent pas au projet du premier DMO sélectionné
            const selectedProject = selectedOptions.first().data('project');
            $select.find('option').each(function() {
                const isMatchingProject = $(this).data('project') === selectedProject;
                $(this).prop('disabled', !isMatchingProject);
            });
        } else {
            // Réactive le select project et recharge toutes les options via Twig
            $('#project').prop('disabled', false);
            $('#project').empty();
            $('#project').selectpicker('destroy');
            {% for project in projects %}
                {% if package.getProjectRelation is not null and project.otp == package.getProjectRelation.getOTP %}
                    $('#project').append('<option value="{{ project.id }}" selected>{{ project.otp }}</option>');
                {% else %}
                    $('#project').append('<option value="{{ project.id }}">{{ project.otp }}</option>');
                {% endif %}
            {% endfor %}
            $('#project').selectpicker();
            
            // Réactive toutes les options du select DMO
            $select.find('option').each(function() {
                $(this).prop('disabled', false);
            });
        }
        // Réinitialise le select DMO pour refléter les changements
        $select.selectpicker('destroy');
        $select.selectpicker();
        $select.selectpicker('toggle');
    }

    $(document).on('change', '#dmo', function() {
        handleDMOSelection($(this));
    });

    handleDMOSelection($('#dmo'));
    $('#dmo').selectpicker('toggle');

    function updatePackage(){
        let id = {{ package.id }};
        let owner = $('#owner').text().trim().split(' ')[1];
        let activity = $('#activity').val();
        let ex = $('#ex-package').val();
        let dmo = $('#dmo').val();
        let project = $('#project').val();
        let description = $('#description').val();

        $.ajax({
            url: "{{ path('edit_package', {'id': package.id}) }}",
            type: 'POST',
            data: {
                owner: owner,
                activity: activity,
                ex: ex,
                dmo: dmo,
                project: project,
                description: description
            },
            success: function(response){
                Toast.fire({
                    icon: 'success',
                    title: 'Package mise à jour !'
                });
            },
            error: function(response){
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors de la mise à jour !'
                });
            }
        });
    }

    $('#update').click(function(){
        updatePackage();
    });

    function releasePackage(){
        let id = {{ package.id }};
        $.ajax({
            url: "{{ path('verification_package', {'id': package.id}) }}",
            type: 'POST',
            data: {
                valid: $('#valid').val()
            },
            success: function(response){
                Toast.fire({
                    icon: 'success',
                    title: 'Vérification réussie !'
                });
                window.location.href = "{{ path('app_package') }}#onlget=BE";
            },
            error: function(response){
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors du lancement de la vérification !'
                });
            }
        });
    }

    function are_u_sure(){
        Swal.fire({
            title: 'Êtes-vous sûr ?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, vérifier ce package !',
            cancelButtonText: 'Annuler',
            customClass: {
                confirmButton: 'btn btn-sm btn-success',
                cancelButton: 'btn btn-sm btn-danger'
            },
        }).then((result) => {
            if (result.isConfirmed) {
                updatePackage();
                releasePackage();
            }
        });
    }

    $('#verif_package').click(function(){
        are_u_sure();
    });

    $('#verif').click(function(){
        if($('#table-documents tbody tr').length > 0){
            $('#modal_release').modal('show');
        }else{
            Toast.fire({
                icon: 'error',
                title: 'Aucun document à release !'
            });
        }
    });

    let prodDraw = "";
    let prodDrawRev = "";

    function refreshIframe() {
        if ((prodDraw!==$('#prodDraw').val() || prodDrawRev!==$('#prodDrawRev').val()) && $('#prodDraw').val() && $('#prodDrawRev').val()) {
            prodDraw = $('#prodDraw').val();
            prodDrawRev = $('#prodDrawRev').val();
            $('#aletiq_preview').attr('src', "https://app.aletiq.com/parts/preview/id/"+prodDraw+"/revision/"+prodDrawRev);
        }
        else {
            prodDraw = "";
            prodDrawRev = "";
            $('#aletiq_preview').attr('src', "");
        }
    }

    $('#prodDraw').change(refreshIframe);
    $('#prodDrawRev').change(refreshIframe);

    $(function () {
        $('[data-bs-toggle="tooltip"]').tooltip()
    })

    function validate_document_data() {
        let input_list = ['reference', 'refRev', 'prodDraw', 'prodDrawRev', 'alias', 'refTitleFra', 'action', 'docType', 'materialType', 'inventoryImpact', 'ex', 'custDrawing', 'custDrawingRev', 'weight', 'weightUnit', 'material', 'platingSurface', 'platingSurfaceUnit', 'eccn', 'rdo', 'hts', 'comments'];
        var required = true;
        for (let i = 0; i < input_list.length; i++) {
            if ($('#' + input_list[i]).prop('required') && $('#' + input_list[i]).val() === "") {
                Toast.fire({
                    icon: 'error',
                    title: 'Veuillez remplir tous les champs obligatoires !'
                });
                required = false;
                if ($('#' + input_list[i]).hasClass('selectpicker')) {
                    $('#' + input_list[i]).next().addClass('border-danger');
                } else {
                    $('#' + input_list[i]).addClass('border-danger');
                }
            } else {
                if ($('#' + input_list[i]).hasClass('selectpicker')) {
                    $('#' + input_list[i]).next().removeClass('border-danger');
                } else {
                    $('#' + input_list[i]).removeClass('border-danger');
                }
            }
        }
        return required;
    }

    function collect_data_document() {
        let reference = $('#reference').val();
        let refRev = $('#refRev').val();
        let prodDraw = $('#prodDraw').val();
        let prodDrawRev = $('#prodDrawRev').val();
        let alias = $('#alias').val();
        let refTitleFra = $('#refTitleFra').val();
        let action = $('#action').val();
        let docType = $('#docType').val();
        let materialType = $('#materialType').val();
        let inventoryImpact = $('#inventoryImpact').val();
        let ex = $('#ex').val();
        let custDrawing = $('#custDrawing').val();
        let custDrawingRev = $('#custDrawingRev').val();
        let weight = $('#weight').val();
        let weightUnit = $('#weightUnit').val();
        let material = $('#material').val();
        let platingSurface = $('#platingSurface').val();
        let platingSurfaceUnit = $('#platingSurfaceUnit').val();
        let internalMachRec = $('#internalMachRec').is(':checked') ? 1 : 0;
        let eccn = $('#eccn').val();
        let rdo = $('#rdo').val();
        let hts = $('#hts').val();
        let comments = $('#comments').val();
        return {
            reference: reference,
            refRev: refRev,
            prodDraw: prodDraw,
            prodDrawRev: prodDrawRev,
            alias: alias,
            refTitleFra: refTitleFra,
            action: action,
            docType: docType,
            materialType: materialType,
            inventoryImpact: inventoryImpact,
            ex: ex,
            custDrawing: custDrawing,
            custDrawingRev: custDrawingRev,
            weight: weight,
            weightUnit: weightUnit,
            material: material,
            platingSurface: platingSurface,
            platingSurfaceUnit: platingSurfaceUnit,
            internalMachRec: internalMachRec,
            eccn: eccn,
            rdo: rdo,
            hts: hts,
            comments: comments,
        };
    }

    function empty_fields() {
        $('#reference').val('');
        $('#refRev').val('');
        $('#prodDraw').val('');
        $('#prodDrawRev').val('');
        $('#alias').val('');
        $('#refTitleFra').val('');
        $('#action').selectpicker('val', '');
        $('#docType').selectpicker('val', '');
        $('#internalMachRec').prop('checked', false);
        $('#materialType').selectpicker('val', '');
        $('#inventoryImpact').selectpicker('val', '');
        $('#ex').selectpicker('val', '');
        $('#custDrawing').val('');
        $('#custDrawingRev').val('');
        $('#weight').val('');
        $('#weightUnit').selectpicker('val', '');
        $('#material').selectpicker('val', '');
        $('#platingSurface').val('');
        $('#platingSurfaceUnit').selectpicker('val', '');
        $('#eccn').val('');
        $('#rdo').val('');
        $('#hts').val('');
        $('#comments').val('');
        refreshIframe();
    }


    function change_border(input) {
        if (input.prop('required') && input.val() === "") {
            if (input.hasClass('selectpicker')) {
                input.next().addClass('border-danger');
            } else {
                input.addClass('border-danger');
            }
        } else {
            if (input.hasClass('selectpicker')) {
                input.next().removeClass('border-danger');
            } else {
                input.removeClass('border-danger');
            }
        }
    }

    $('input, select').change(function() {
        change_border($(this));
    });

    function update_document(){
        let data = collect_data_document();
        if (!validate_document_data()) return;

        let url = "{{ path('maj_document', {'id': 0}) }}";
        url = url.replace('0', $('#document_id').val());

        data = Object.fromEntries(Object.entries(data).filter(([key, value]) => value !== ""));
        $.ajax({
            url: url,
            type: 'POST',
            data: data,
            success: function(response){
                Toast.fire({
                    icon: 'success',
                    title: 'Document mis à jour !'
                });

                $('#update-document').hide();
                empty_fields();
                refresh_table_documents();
            },
            error: function(response){
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors de la mise à jour du document !'
                });
            }
        });
    }

    $('#update-document').click(function(){
        update_document();
    });


    function refresh_table_documents(){
        $.ajax({
            url: "{{ path('verif_package', {'id': package.id}) }}",
            type: 'POST',
            success: function(response){
                $('#div-documents').html($(response).find('#div-documents').html());
                $(function () {
                    $('[data-bs-toggle="tooltip"]').tooltip()
                })
            },
            error: function(response){
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors de la mise à jour des documents !'
                });
            }
        });
    }


    function fill_fields_document(){
        let row = $(this);
        if (row.hasClass('selected-row')) {
            empty_fields();
            row.removeClass('selected-row');
            $('#add-document').show();
            $('#update-document').hide();
            $('#delete-document').hide();
            return;
        }
        let id = row.attr('id').split('_')[1];
        let reference = row.find('.reference').text();
        let refRev = row.find('.refRev').text();
        let prodDraw = row.find('.prodDraw').text();
        let prodDrawRev = row.find('.prodDrawRev').text();
        let alias = row.find('.alias').text();
        let refTitleFra = row.find('.refTitleFra').text();
        let action = row.find('.action').text();
        let docType = row.find('.docType').text();
        let internalMachRec = row.find('.internalMachRec').find('img').length ? 1 : 0;
        let materialType = row.find('.materialType').text();
        let inventoryImpact = row.find('.inventoryImpact').text();
        let ex = row.find('.ex').text().trim();
        let custDrawing = row.find('.custDrawing').text();
        let custDrawingRev = row.find('.custDrawingRev').text();
        let weight = row.find('.weight').text();
        let weightUnit = row.find('.weightUnit').text();
        let material = row.find('.material').text();
        let platingSurface = row.find('.platingSurface').text();
        let platingSurfaceUnit = row.find('.platingSurfaceUnit').text();
        let eccn = row.find('.eccn').text();
        let rdo = row.find('.rdo').text();
        let hts = row.find('.hts').text();
        let comments = row.find('.comments').find('i').attr('data-bs-title');

        $('#document_id').val(id);
        $('#reference').val(reference);
        $('#refRev').val(refRev);
        $('#prodDraw').val(prodDraw);
        $('#prodDrawRev').val(prodDrawRev);
        $('#alias').val(alias);
        $('#refTitleFra').val(refTitleFra);
        $('#action').selectpicker('val', action);
        $('#docType').selectpicker('val', docType);
        $('#internalMachRec').prop('checked', internalMachRec);
        $('#materialType').selectpicker('val', materialType);
        $('#inventoryImpact').selectpicker('val', inventoryImpact);
        $('#ex').selectpicker('val', ex);
        $('#custDrawing').val(custDrawing);
        $('#custDrawingRev').val(custDrawingRev);
        $('#weight').val(weight);
        $('#weightUnit').selectpicker('val', weightUnit);
        $('#material').selectpicker('val', material);
        $('#platingSurface').val(platingSurface);
        $('#platingSurfaceUnit').selectpicker('val', platingSurfaceUnit);
        $('#eccn').val(eccn);
        $('#rdo').val(rdo);
        $('#hts').val(hts);
        $('#comments').val(comments);

        $('.document_row').removeClass('selected-row');
        row.addClass('selected-row');

        $('#add-document').hide();
        $('#update-document').show();
        $('#delete-document').show();
        refreshIframe();
    }

    
    $(document).on('click', '.document_row', function(e) {
        if ($(e.target).closest('.copy_icone').length) {
            copy_icone.call(this);
            return;
        }
        fill_fields_document.call(this, e);
    });


    function copy_icone(){
        empty_fields();
        let row = $(this).closest('.document_row');
        let reference = row.find('.reference').text();
        let refRev = row.find('.refRev').text();
        let prodDraw = row.find('.prodDraw').text();
        let prodDrawRev = row.find('.prodDrawRev').text();
        let alias = row.find('.alias').text();
        let refTitleFra = row.find('.refTitleFra').text();
        let action = row.find('.action').text();
        let docType = row.find('.docType').text();
        let internalMachRec = row.find('.internalMachRec').length ? 1 : 0;
        let materialType = row.find('.materialType').text();
        let inventoryImpact = row.find('.inventoryImpact').text();
        let ex = row.find('.ex').text();
        let custDrawing = row.find('.custDrawing').text();
        let custDrawingRev = row.find('.custDrawingRev').text();
        let weight = row.find('.weight').text();
        let weightUnit = row.find('.weightUnit').text();
        let material = row.find('.material').text();
        let platingSurface = row.find('.platingSurface').text();
        let platingSurfaceUnit = row.find('.platingSurfaceUnit').text();
        let eccn = row.find('.eccn').text();
        let rdo = row.find('.rdo').text();
        let hts = row.find('.hts').text();
        let comments = row.find('.comments').find('i').attr('data-bs-title');
        
        $('#reference').val(reference);
        $('#refRev').val(refRev);
        $('#prodDraw').val(prodDraw);
        $('#prodDrawRev').val(prodDrawRev);
        $('#alias').val(alias);
        $('#refTitleFra').val(refTitleFra);
        $('#action').selectpicker('val', action);
        $('#docType').selectpicker('val', docType);
        $('#internalMachRec').prop('checked', internalMachRec);
        $('#materialType').selectpicker('val', materialType);
        $('#inventoryImpact').selectpicker('val', inventoryImpact);
        $('#ex').selectpicker('val', ex);
        $('#custDrawing').val(custDrawing);
        $('#custDrawingRev').val(custDrawingRev);
        $('#weight').val(weight);
        $('#weightUnit').selectpicker('val', weightUnit);
        $('#material').selectpicker('val', material);
        $('#platingSurface').val(platingSurface);
        $('#platingSurfaceUnit').selectpicker('val', platingSurfaceUnit);
        $('#eccn').val(eccn);
        $('#rdo').val(rdo);
        $('#hts').val(hts);
        $('#comments').val(comments);

        refreshIframe();
        Toast.fire({
            icon: 'success',
            title: 'Données copiées !'
        });        
    };


    $(document).on('click', '.copy_icone', function(e){
        copy_icone();
    });


    function refuser_package(){
        $.ajax({
            url: "{{ path('refuser_package', {'id': package.id}) }}",
            type: 'POST',
            success: function(response){
                Toast.fire({
                    icon: 'success',
                    title: 'Package renvoyé !'
                });
                window.location.href = "{{ path('app_package') }}";
            },
            error: function(response){
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors du renvoi du package !'
                });
            }
        });
    }

    $('#refuser').click(function(){
        Swal.fire({
            title: 'Êtes-vous sûr ?',
            text: "Vous ne pourrez pas revenir en arrière !",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, renvoyer !',
            cancelButtonText: 'Annuler',
            customClass: {
                confirmButton: 'btn btn-sm btn-success',
                cancelButton: 'btn btn-sm btn-danger'
            },
        }).then((result) => {
            if (result.isConfirmed) {
                refuser_package();
            }
        });
    });
</script>

{% endblock %}
